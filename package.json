{"name": "seo-saas-html", "version": "1.0.0", "description": "World's most advanced AI-powered SEO content generation platform", "main": "index.js", "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "typecheck": "tsc --noEmit", "backend:dev": "cd backend && npm run dev", "backend:start": "cd backend && npm start", "backend:test": "cd backend && npm test"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@types/node": "^20.10.6", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "tailwindcss": "^3.4.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@supabase/supabase-js": "^2.39.3", "@supabase/ssr": "^0.0.10", "openai": "^4.104.0", "axios": "^1.6.2", "lodash": "^4.17.21", "@types/lodash": "^4.14.202", "framer-motion": "^10.16.16", "lucide-react": "^0.302.0", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "react-query": "^3.39.3", "recharts": "^2.8.0", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.2.0", "@heroicons/react": "^2.0.18"}, "devDependencies": {"eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "eslint-config-prettier": "^9.1.0", "prettier": "^3.1.1", "jest": "^29.7.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.6", "jest-environment-jsdom": "^29.7.0", "tailwindcss-animate": "^1.0.7"}, "keywords": ["seo", "content-generation", "ai", "next.js", "typescript", "supabase"], "author": "SEO SAAS Team", "license": "MIT"}