/**
 * Projects List Page
 * Enterprise SEO SAAS - Project Management Interface
 * Real data only - NO demo/mock content allowed
 */

'use client'

import { useState, useEffect } from 'react'
import DashboardLayout from '@/components/Layout/DashboardLayout'
import ProjectCard from '@/components/Projects/ProjectCard'
import ProjectFilters from '@/components/Projects/ProjectFilters'
import CreateProjectModal from '@/components/Projects/CreateProjectModal'
import { Project, ProjectFilters as FilterType, ProjectStats } from '@/types/project'
import { filterProjects, sortProjects } from '@/utils/projectHelpers'
import { 
  MagnifyingGlassIcon, 
  PlusIcon, 
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  AdjustmentsHorizontalIcon
} from '@heroicons/react/24/outline'

export default function ProjectsPage() {
  const [projects, setProjects] = useState<Project[]>([])
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [showFilters, setShowFilters] = useState(false)
  
  // Filter and search state
  const [filters, setFilters] = useState<FilterType>({
    search: '',
    status: [],
    industry: [],
    sortBy: 'updatedAt',
    sortOrder: 'desc'
  })

  const [projectStats, setProjectStats] = useState<ProjectStats>({
    totalProjects: 0,
    activeProjects: 0,
    totalContent: 0,
    totalKeywords: 0,
    averageSeoScore: 0,
    projectsByStatus: {
      draft: 0,
      active: 0,
      paused: 0,
      completed: 0,
      archived: 0
    },
    projectsByIndustry: {
      technology: 0,
      healthcare: 0,
      finance: 0,
      education: 0,
      ecommerce: 0,
      real_estate: 0,
      automotive: 0,
      food_beverage: 0,
      fashion: 0,
      travel: 0,
      fitness: 0,
      marketing: 0,
      consulting: 0,
      manufacturing: 0,
      retail: 0,
      construction: 0,
      legal: 0,
      entertainment: 0,
      sports: 0,
      nonprofit: 0,
      other: 0
    },
    recentActivity: []
  })

  // Load projects on component mount
  useEffect(() => {
    loadProjects()
  }, [])

  // Apply filters and sorting when projects or filters change
  useEffect(() => {
    const filtered = filterProjects(projects, filters)
    const sorted = sortProjects(filtered, filters.sortBy || 'updatedAt', filters.sortOrder || 'desc')
    setFilteredProjects(sorted)
  }, [projects, filters])

  const loadProjects = async () => {
    setIsLoading(true)
    try {
      // In real implementation, this would fetch from Supabase
      // For now, we start with empty array (no demo data)
      const userProjects: Project[] = []
      
      setProjects(userProjects)
      calculateProjectStats(userProjects)
    } catch (error) {
      console.error('Error loading projects:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const calculateProjectStats = (projectList: Project[]) => {
    const stats: ProjectStats = {
      totalProjects: projectList.length,
      activeProjects: projectList.filter(p => p.status === 'active').length,
      totalContent: projectList.reduce((sum, p) => sum + p.metrics.totalContent, 0),
      totalKeywords: projectList.reduce((sum, p) => sum + p.metrics.keywordsTracked, 0),
      averageSeoScore: projectList.length > 0 
        ? projectList.reduce((sum, p) => sum + p.metrics.averageSeoScore, 0) / projectList.length 
        : 0,
      projectsByStatus: {
        draft: projectList.filter(p => p.status === 'draft').length,
        active: projectList.filter(p => p.status === 'active').length,
        paused: projectList.filter(p => p.status === 'paused').length,
        completed: projectList.filter(p => p.status === 'completed').length,
        archived: projectList.filter(p => p.status === 'archived').length
      },
      projectsByIndustry: {
        technology: projectList.filter(p => p.industry === 'technology').length,
        healthcare: projectList.filter(p => p.industry === 'healthcare').length,
        finance: projectList.filter(p => p.industry === 'finance').length,
        education: projectList.filter(p => p.industry === 'education').length,
        ecommerce: projectList.filter(p => p.industry === 'ecommerce').length,
        real_estate: projectList.filter(p => p.industry === 'real_estate').length,
        automotive: projectList.filter(p => p.industry === 'automotive').length,
        food_beverage: projectList.filter(p => p.industry === 'food_beverage').length,
        fashion: projectList.filter(p => p.industry === 'fashion').length,
        travel: projectList.filter(p => p.industry === 'travel').length,
        fitness: projectList.filter(p => p.industry === 'fitness').length,
        marketing: projectList.filter(p => p.industry === 'marketing').length,
        consulting: projectList.filter(p => p.industry === 'consulting').length,
        manufacturing: projectList.filter(p => p.industry === 'manufacturing').length,
        retail: projectList.filter(p => p.industry === 'retail').length,
        construction: projectList.filter(p => p.industry === 'construction').length,
        legal: projectList.filter(p => p.industry === 'legal').length,
        entertainment: projectList.filter(p => p.industry === 'entertainment').length,
        sports: projectList.filter(p => p.industry === 'sports').length,
        nonprofit: projectList.filter(p => p.industry === 'nonprofit').length,
        other: projectList.filter(p => p.industry === 'other').length
      },
      recentActivity: []
    }
    
    setProjectStats(stats)
  }

  const handleSearchChange = (value: string) => {
    setFilters(prev => ({ ...prev, search: value }))
  }

  const handleFilterChange = (newFilters: Partial<FilterType>) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
  }

  const handleProjectCreated = (newProject: Project) => {
    setProjects(prev => [newProject, ...prev])
    setShowCreateModal(false)
  }

  return (
    <DashboardLayout 
      title="Projects" 
      subtitle="Manage your SEO content projects"
    >
      {/* Page Header with Actions */}
      <div className="mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          {/* Search Bar */}
          <div className="relative flex-1 max-w-md">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search projects..."
              value={filters.search || ''}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg bg-white text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-3">
            {/* View Toggle */}
            <div className="flex rounded-lg border border-gray-300 bg-white p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-1.5 rounded ${
                  viewMode === 'grid'
                    ? 'bg-blue-500 text-white'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <Squares2X2Icon className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-1.5 rounded ${
                  viewMode === 'list'
                    ? 'bg-blue-500 text-white'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <ListBulletIcon className="h-4 w-4" />
              </button>
            </div>

            {/* Filters Button */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <AdjustmentsHorizontalIcon className="h-4 w-4 mr-2" />
              Filters
            </button>

            {/* Create Project Button */}
            <button
              onClick={() => setShowCreateModal(true)}
              className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              New Project
            </button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="mt-6 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="text-2xl font-bold text-gray-900">{projectStats.totalProjects}</div>
            <div className="text-sm text-gray-500">Total Projects</div>
          </div>
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="text-2xl font-bold text-green-600">{projectStats.activeProjects}</div>
            <div className="text-sm text-gray-500">Active Projects</div>
          </div>
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="text-2xl font-bold text-blue-600">{projectStats.totalContent}</div>
            <div className="text-sm text-gray-500">Total Content</div>
          </div>
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="text-2xl font-bold text-purple-600">{projectStats.totalKeywords}</div>
            <div className="text-sm text-gray-500">Keywords Tracked</div>
          </div>
        </div>
      </div>

      {/* Filters Panel */}
      {showFilters && (
        <div className="mb-6">
          <ProjectFilters 
            filters={filters}
            onFilterChange={handleFilterChange}
            projectStats={projectStats}
          />
        </div>
      )}

      {/* Projects Grid/List */}
      <div className="mb-8">
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        ) : filteredProjects.length > 0 ? (
          <div className={`
            ${viewMode === 'grid' 
              ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' 
              : 'space-y-4'
            }
          `}>
            {filteredProjects.map((project) => (
              <ProjectCard 
                key={project.id} 
                project={project} 
                viewMode={viewMode}
                onProjectUpdate={(updatedProject) => {
                  setProjects(prev => prev.map(p => 
                    p.id === updatedProject.id ? updatedProject : p
                  ))
                }}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="mx-auto h-24 w-24 text-gray-400 mb-4">
              <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No projects found</h3>
            <p className="text-gray-500 mb-6">
              {filters.search || filters.status?.length || filters.industry?.length
                ? 'Try adjusting your search or filters'
                : 'Get started by creating your first SEO project'
              }
            </p>
            <button
              onClick={() => setShowCreateModal(true)}
              className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Create Your First Project
            </button>
          </div>
        )}
      </div>

      {/* Create Project Modal */}
      {showCreateModal && (
        <CreateProjectModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onProjectCreated={handleProjectCreated}
        />
      )}
    </DashboardLayout>
  )
}