/**
 * Project Details Page
 * Enterprise SEO SAAS - Comprehensive project dashboard with analytics and management
 */

'use client'

import { useState, useEffect } from 'react'
import { useParams, useRouter } from 'next/navigation'
import DashboardLayout from '@/components/Layout/DashboardLayout'
import ProjectHeader from '@/components/Projects/ProjectHeader'
import ProjectMetrics from '@/components/Projects/ProjectMetrics'
import ContentGrid from '@/components/Projects/ContentGrid'
import KeywordsTable from '@/components/Projects/KeywordsTable'
import CompetitorPanel from '@/components/Projects/CompetitorPanel'
import QuickActions from '@/components/Projects/QuickActions'
import ProjectSettings from '@/components/Projects/ProjectSettings'
import { Project } from '@/types/project'
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline'

export default function ProjectDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const projectId = params.id as string

  const [project, setProject] = useState<Project | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'overview' | 'content' | 'keywords' | 'competitors' | 'settings'>('overview')

  useEffect(() => {
    loadProject()
  }, [projectId])

  const loadProject = async () => {
    setIsLoading(true)
    setError(null)

    try {
      // In real implementation, this would fetch from Supabase
      // For now, we'll simulate loading (no demo data)
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Since we haven't implemented backend yet, we'll show empty state
      // In production, this would fetch real user project data
      setProject(null)
      setError('Project not found. This would load real user data from the database.')
      
    } catch (err) {
      console.error('Error loading project:', err)
      setError('Failed to load project. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleProjectUpdate = (updatedProject: Project) => {
    setProject(updatedProject)
  }

  const handleProjectDelete = () => {
    // Navigate back to projects list after deletion
    router.push('/projects')
  }

  // Loading state
  if (isLoading) {
    return (
      <DashboardLayout title="Loading Project..." subtitle="Please wait">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    )
  }

  // Error state
  if (error) {
    return (
      <DashboardLayout title="Project Error" subtitle="Unable to load project">
        <div className="max-w-lg mx-auto text-center py-12">
          <ExclamationTriangleIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Project Not Found</h3>
          <p className="text-gray-600 mb-6">{error}</p>
          <div className="space-x-3">
            <button
              onClick={() => router.push('/projects')}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
            >
              Back to Projects
            </button>
            <button
              onClick={loadProject}
              className="px-4 py-2 border border-gray-300 hover:bg-gray-50 text-gray-700 font-medium rounded-lg transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  // Project not found state
  if (!project) {
    return (
      <DashboardLayout title="Project Not Found" subtitle="The requested project could not be found">
        <div className="max-w-lg mx-auto text-center py-12">
          <div className="w-24 h-24 mx-auto mb-6 text-gray-400">
            <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Project Not Found</h3>
          <p className="text-gray-600 mb-6">
            The project with ID "{projectId}" does not exist or you don't have permission to access it.
          </p>
          <button
            onClick={() => router.push('/projects')}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
          >
            Back to Projects
          </button>
        </div>
      </DashboardLayout>
    )
  }

  // Main project dashboard
  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Project Header */}
        <ProjectHeader 
          project={project}
          onProjectUpdate={handleProjectUpdate}
          onProjectDelete={handleProjectDelete}
        />

        {/* Navigation Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { key: 'overview', label: 'Overview', count: null },
              { key: 'content', label: 'Content', count: project.metrics.totalContent },
              { key: 'keywords', label: 'Keywords', count: project.keywords.length },
              { key: 'competitors', label: 'Competitors', count: project.competitors.length },
              { key: 'settings', label: 'Settings', count: null }
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as any)}
                className={`
                  py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap
                  ${activeTab === tab.key
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                {tab.label}
                {tab.count !== null && (
                  <span className={`
                    ml-2 py-0.5 px-2 rounded-full text-xs
                    ${activeTab === tab.key
                      ? 'bg-blue-100 text-blue-600'
                      : 'bg-gray-100 text-gray-600'
                    }
                  `}>
                    {tab.count}
                  </span>
                )}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="min-h-screen">
          {activeTab === 'overview' && (
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
              <div className="lg:col-span-8 space-y-6">
                <ProjectMetrics project={project} />
                <div className="bg-white rounded-lg border border-gray-200 p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Content</h3>
                  <ContentGrid project={project} limit={5} showActions={false} />
                </div>
              </div>
              <div className="lg:col-span-4 space-y-6">
                <QuickActions project={project} onProjectUpdate={handleProjectUpdate} />
                <div className="bg-white rounded-lg border border-gray-200 p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Top Keywords</h3>
                  <KeywordsTable project={project} limit={5} showActions={false} />
                </div>
              </div>
            </div>
          )}

          {activeTab === 'content' && (
            <div className="bg-white rounded-lg border border-gray-200">
              <ContentGrid project={project} onProjectUpdate={handleProjectUpdate} />
            </div>
          )}

          {activeTab === 'keywords' && (
            <div className="bg-white rounded-lg border border-gray-200">
              <KeywordsTable project={project} onProjectUpdate={handleProjectUpdate} />
            </div>
          )}

          {activeTab === 'competitors' && (
            <CompetitorPanel project={project} onProjectUpdate={handleProjectUpdate} />
          )}

          {activeTab === 'settings' && (
            <ProjectSettings project={project} onProjectUpdate={handleProjectUpdate} />
          )}
        </div>
      </div>
    </DashboardLayout>
  )
}