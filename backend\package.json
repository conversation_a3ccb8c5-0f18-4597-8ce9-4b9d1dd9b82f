{"name": "seo-saas-backend", "version": "1.0.0", "description": "Backend for SEO SAAS HTML - AI-Powered SEO Content Generation Platform", "main": "server.js", "type": "module", "scripts": {"dev": "nodemon server.js", "start": "node server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "helmet": "^7.1.0", "compression": "^1.7.4", "openai": "^4.104.0", "puppeteer": "^21.5.0", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "serpapi": "^2.0.0", "newsapi": "^2.4.1", "natural": "^6.10.4", "compromise": "^14.10.0", "keyword-extractor": "^0.0.25", "sentiment": "^5.0.2", "cheerio": "^1.0.0-rc.12", "readability": "^0.1.1", "text-readability": "^0.2.0", "flesch": "^2.0.0", "@supabase/supabase-js": "^2.39.3", "mongodb": "^6.3.0", "mongoose": "^8.0.3", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "axios": "^1.6.2", "lodash": "^4.17.21", "moment": "^2.29.4", "winston": "^3.11.0", "bull": "^4.12.2", "python-shell": "^5.0.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "express-session": "^1.17.3", "connect-mongo": "^5.1.0", "multer": "^1.4.5-lts.1", "sharp": "^0.33.1", "uuid": "^9.0.1", "csv-parser": "^3.0.0", "json2csv": "^6.1.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "@types/jest": "^29.5.8"}, "keywords": ["seo", "content-generation", "ai", "backend", "express", "nodejs"], "author": "SEO SAAS Team", "license": "MIT"}