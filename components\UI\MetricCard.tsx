'use client';

import React from 'react';
import { 
  ArrowUpIcon, 
  ArrowDownIcon,
  ChartBarIcon 
} from '@heroicons/react/24/outline';

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    type: 'increase' | 'decrease';
    period?: string;
  };
  icon?: React.ComponentType<{ className?: string }>;
  trend?: Array<{ value: number; label: string }>;
  loading?: boolean;
  className?: string;
}

export default function MetricCard({
  title,
  value,
  change,
  icon: Icon = ChartBarIcon,
  trend,
  loading = false,
  className = ''
}: MetricCardProps) {
  if (loading) {
    return (
      <div className={`metric-card loading ${className}`}>
        <div className="animate-pulse">
          <div className="flex items-center justify-between mb-4">
            <div className="h-4 bg-gray-200 rounded w-24"></div>
            <div className="h-6 w-6 bg-gray-200 rounded"></div>
          </div>
          <div className="h-8 bg-gray-200 rounded w-20 mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-32"></div>
        </div>
      </div>
    );
  }

  const formatValue = (val: string | number) => {
    if (typeof val === 'number') {
      if (val >= 1000000) {
        return `${(val / 1000000).toFixed(1)}M`;
      } else if (val >= 1000) {
        return `${(val / 1000).toFixed(1)}K`;
      }
      return val.toLocaleString();
    }
    return val;
  };

  return (
    <div className={`metric-card ${className}`}>
      <div className="metric-header">
        <h3 className="metric-title">{title}</h3>
        <Icon className="metric-icon" />
      </div>
      
      <div className="metric-value">
        {formatValue(value)}
      </div>
      
      {change && (
        <div className={`metric-change ${change.type === 'increase' ? 'positive' : 'negative'}`}>
          {change.type === 'increase' ? (
            <ArrowUpIcon className="w-3 h-3 mr-1" />
          ) : (
            <ArrowDownIcon className="w-3 h-3 mr-1" />
          )}
          <span>
            {change.type === 'increase' ? '+' : '-'}{Math.abs(change.value)}%
            {change.period && ` ${change.period}`}
          </span>
        </div>
      )}
      
      {trend && trend.length > 0 && (
        <div className="mt-4">
          <div className="flex items-end space-x-1 h-8">
            {trend.map((point, index) => {
              const maxValue = Math.max(...trend.map(p => p.value));
              const height = (point.value / maxValue) * 100;
              
              return (
                <div
                  key={index}
                  className="flex-1 bg-blue-200 rounded-t"
                  style={{ height: `${height}%` }}
                  title={`${point.label}: ${point.value}`}
                />
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}

export function MetricCardSkeleton({ className = '' }: { className?: string }) {
  return (
    <div className={`metric-card ${className}`}>
      <div className="animate-pulse">
        <div className="flex items-center justify-between mb-4">
          <div className="h-4 bg-gray-200 rounded w-24"></div>
          <div className="h-6 w-6 bg-gray-200 rounded"></div>
        </div>
        <div className="h-8 bg-gray-200 rounded w-20 mb-2"></div>
        <div className="h-3 bg-gray-200 rounded w-32"></div>
      </div>
    </div>
  );
}