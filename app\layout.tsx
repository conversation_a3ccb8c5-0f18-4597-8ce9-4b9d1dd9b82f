import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { AuthProvider } from '@/hooks/useAuth';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'SEO SAAS - AI-Powered SEO Content Generation Platform',
  description: 'World\'s most advanced AI-powered SEO content generation platform that works for ANY keyword in ANY industry using real competitor intelligence.',
  keywords: 'SEO, content generation, AI, competitor analysis, SERP analysis',
  authors: [{ name: 'SEO SAAS Team' }],
  openGraph: {
    title: 'SEO SAAS - AI-Powered SEO Content Generation Platform',
    description: 'Dominate ANY niche with AI-generated content based on deep competitor research and real-time analysis.',
    type: 'website',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="theme-color" content="#000000" />
        <link rel="icon" href="/favicon.ico" />
      </head>
      <body className={inter.className} suppressHydrationWarning>
        <AuthProvider>
          <div id="root">
            {children}
          </div>
        </AuthProvider>
      </body>
    </html>
  );
}