'use client';

import React from 'react';
import Link from 'next/link';
import {
  DocumentTextIcon,
  MagnifyingGlassIcon,
  ChartBarIcon,
  FolderIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

interface ActivityItem {
  id: string;
  type: 'content_generated' | 'analysis_completed' | 'project_created' | 'research_done';
  title: string;
  description: string;
  timestamp: string;
  link?: string;
  metadata?: {
    keyword?: string;
    word_count?: number;
    seo_score?: number;
  };
}

interface RecentActivityProps {
  activities: ActivityItem[];
  loading?: boolean;
  className?: string;
}

const activityIcons = {
  content_generated: DocumentTextIcon,
  analysis_completed: ChartBarIcon,
  project_created: FolderIcon,
  research_done: MagnifyingGlassIcon,
};

const activityColors = {
  content_generated: 'text-green-600 bg-green-100',
  analysis_completed: 'text-blue-600 bg-blue-100',
  project_created: 'text-purple-600 bg-purple-100',
  research_done: 'text-orange-600 bg-orange-100',
};

export default function RecentActivity({ activities, loading = false, className = '' }: RecentActivityProps) {
  if (loading) {
    return (
      <div className={`bg-white rounded-xl border border-gray-200 ${className}`}>
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {[...Array(5)].map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-gray-200 rounded-lg"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2 mb-1"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/4"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (activities.length === 0) {
    return (
      <div className={`bg-white rounded-xl border border-gray-200 ${className}`}>
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
        </div>
        <div className="p-6 text-center">
          <ClockIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
          <p className="text-gray-500 mb-4">No recent activity</p>
          <Link
            href="/content/create"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700"
          >
            Create Your First Content
          </Link>
        </div>
      </div>
    );
  }

  const formatTimeAgo = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return date.toLocaleDateString();
  };

  return (
    <div className={`bg-white rounded-xl border border-gray-200 ${className}`}>
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
          <Link
            href="/activity"
            className="text-sm text-blue-600 hover:text-blue-800 font-medium"
          >
            View All
          </Link>
        </div>
      </div>
      
      <div className="p-6">
        <div className="space-y-4">
          {activities.slice(0, 6).map((activity) => {
            const Icon = activityIcons[activity.type];
            const colorClass = activityColors[activity.type];
            
            return (
              <div key={activity.id} className="flex items-start space-x-3">
                <div className={`p-2 rounded-lg ${colorClass}`}>
                  <Icon className="w-4 h-4" />
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {activity.link ? (
                        <Link href={activity.link} className="hover:text-blue-600">
                          {activity.title}
                        </Link>
                      ) : (
                        activity.title
                      )}
                    </p>
                    <span className="text-xs text-gray-500 flex-shrink-0 ml-2">
                      {formatTimeAgo(activity.timestamp)}
                    </span>
                  </div>
                  
                  <p className="text-sm text-gray-600 mt-1">
                    {activity.description}
                  </p>
                  
                  {activity.metadata && (
                    <div className="flex items-center space-x-4 mt-2">
                      {activity.metadata.keyword && (
                        <span className="text-xs text-gray-500">
                          Keyword: <span className="font-medium">{activity.metadata.keyword}</span>
                        </span>
                      )}
                      {activity.metadata.word_count && (
                        <span className="text-xs text-gray-500">
                          {activity.metadata.word_count} words
                        </span>
                      )}
                      {activity.metadata.seo_score && (
                        <span className="text-xs text-gray-500">
                          SEO Score: <span className="font-medium">{activity.metadata.seo_score}%</span>
                        </span>
                      )}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
        
        {activities.length > 6 && (
          <div className="mt-6 text-center">
            <Link
              href="/activity"
              className="text-sm text-blue-600 hover:text-blue-800 font-medium"
            >
              View {activities.length - 6} more activities
            </Link>
          </div>
        )}
      </div>
    </div>
  );
}