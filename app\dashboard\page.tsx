'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { ProtectedRoute } from '@/hooks/useAuth';
import { AuthenticatedLayout, PageHeader, DashboardGrid } from '@/components/Layout/DashboardLayout';
import MetricCard from '@/components/UI/MetricCard';
import RecentActivity from '@/components/UI/RecentActivity';
import {
  DocumentTextIcon,
  ChartBarIcon,
  MagnifyingGlassIcon,
  FolderIcon,
  ClockIcon,
  PlusIcon,
  BoltIcon,
  TrendingUpIcon
} from '@heroicons/react/24/outline';

interface DashboardMetrics {
  contentGenerated: {
    total: number;
    thisMonth: number;
    change: number;
  };
  seoScoreAverage: {
    score: number;
    change: number;
  };
  keywordsTracked: {
    total: number;
    change: number;
  };
  projectsActive: {
    total: number;
    change: number;
  };
  wordsGenerated: {
    total: number;
    thisMonth: number;
    change: number;
  };
  averageRanking: {
    position: number;
    change: number;
  };
}

export default function DashboardPage() {
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [recentActivities, setRecentActivities] = useState([]);
  const [loading, setLoading] = useState(true);

  // Mock data - In real app, this would come from API
  useEffect(() => {
    const fetchDashboardData = async () => {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setMetrics({
        contentGenerated: {
          total: 247,
          thisMonth: 32,
          change: 12.5
        },
        seoScoreAverage: {
          score: 94.2,
          change: 5.3
        },
        keywordsTracked: {
          total: 156,
          change: 8.7
        },
        projectsActive: {
          total: 12,
          change: 2.1
        },
        wordsGenerated: {
          total: 125000,
          thisMonth: 18500,
          change: 15.2
        },
        averageRanking: {
          position: 8.3,
          change: -12.5 // negative is good for ranking
        }
      });

      setRecentActivities([
        {
          id: '1',
          type: 'content_generated',
          title: 'Blog Post Generated',
          description: 'Created "Best SEO Practices for 2024" with 2,500 words',
          timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
          link: '/content/123',
          metadata: {
            keyword: 'SEO best practices',
            word_count: 2500,
            seo_score: 96
          }
        },
        {
          id: '2',
          type: 'analysis_completed',
          title: 'Competitor Analysis Complete',
          description: 'Analyzed 5 competitors for "digital marketing tools"',
          timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          metadata: {
            keyword: 'digital marketing tools'
          }
        },
        {
          id: '3',
          type: 'project_created',
          title: 'New Project Created',
          description: 'Started "E-commerce SEO Campaign" project',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          link: '/projects/456'
        },
        {
          id: '4',
          type: 'research_done',
          title: 'Keyword Research Complete',
          description: 'Found 45 long-tail keywords for fitness niche',
          timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString()
        },
        {
          id: '5',
          type: 'content_generated',
          title: 'Product Description Generated',
          description: 'Created optimized description for "Running Shoes"',
          timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
          metadata: {
            keyword: 'running shoes',
            word_count: 800,
            seo_score: 92
          }
        }
      ]);

      setLoading(false);
    };

    fetchDashboardData();
  }, []);

  const quickActions = [
    {
      name: 'Create Content',
      description: 'Generate SEO-optimized content',
      href: '/content/create',
      icon: DocumentTextIcon,
      color: 'bg-blue-600 hover:bg-blue-700'
    },
    {
      name: 'Analyze Competitors',
      description: 'Research your competition',
      href: '/research/competitors',
      icon: MagnifyingGlassIcon,
      color: 'bg-green-600 hover:bg-green-700'
    },
    {
      name: 'Bulk Generator',
      description: 'Generate multiple articles',
      href: '/content/bulk',
      icon: BoltIcon,
      color: 'bg-purple-600 hover:bg-purple-700'
    },
    {
      name: 'New Project',
      description: 'Start a new SEO project',
      href: '/projects/new',
      icon: FolderIcon,
      color: 'bg-orange-600 hover:bg-orange-700'
    }
  ];

  return (
    <ProtectedRoute>
      <AuthenticatedLayout>
        <div className="space-y-8">
          {/* Page Header */}
          <PageHeader
            title="Dashboard"
            description="Welcome back! Here's what's happening with your SEO campaigns."
            actions={
              <Link
                href="/content/create"
                className="btn btn-primary"
              >
                <PlusIcon className="w-4 h-4 mr-2" />
                Create Content
              </Link>
            }
          />

          {/* Metrics Grid */}
          <DashboardGrid cols={6}>
            <MetricCard
              title="Content Generated"
              value={metrics?.contentGenerated.total || 0}
              change={{
                value: metrics?.contentGenerated.change || 0,
                type: 'increase',
                period: 'this month'
              }}
              icon={DocumentTextIcon}
              loading={loading}
            />
            
            <MetricCard
              title="Average SEO Score"
              value={metrics?.seoScoreAverage.score || 0}
              change={{
                value: metrics?.seoScoreAverage.change || 0,
                type: 'increase',
                period: 'vs last month'
              }}
              icon={TrendingUpIcon}
              loading={loading}
            />
            
            <MetricCard
              title="Keywords Tracked"
              value={metrics?.keywordsTracked.total || 0}
              change={{
                value: metrics?.keywordsTracked.change || 0,
                type: 'increase'
              }}
              icon={MagnifyingGlassIcon}
              loading={loading}
            />
            
            <MetricCard
              title="Active Projects"
              value={metrics?.projectsActive.total || 0}
              change={{
                value: metrics?.projectsActive.change || 0,
                type: 'increase'
              }}
              icon={FolderIcon}
              loading={loading}
            />
            
            <MetricCard
              title="Words Generated"
              value={metrics?.wordsGenerated.thisMonth || 0}
              change={{
                value: metrics?.wordsGenerated.change || 0,
                type: 'increase',
                period: 'this month'
              }}
              icon={DocumentTextIcon}
              loading={loading}
            />
            
            <MetricCard
              title="Avg. Ranking"
              value={`#${metrics?.averageRanking.position || 0}`}
              change={{
                value: Math.abs(metrics?.averageRanking.change || 0),
                type: 'increase', // improved ranking is good
                period: 'positions up'
              }}
              icon={ChartBarIcon}
              loading={loading}
            />
          </DashboardGrid>

          {/* Quick Actions */}
          <div className="bg-white rounded-xl border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">Quick Actions</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {quickActions.map((action) => (
                <Link
                  key={action.name}
                  href={action.href}
                  className="group p-4 rounded-lg border border-gray-200 hover:border-gray-300 transition-all duration-200 hover:shadow-md"
                >
                  <div className="flex items-center mb-3">
                    <div className={`p-2 rounded-lg ${action.color} transition-colors`}>
                      <action.icon className="w-5 h-5 text-white" />
                    </div>
                  </div>
                  <h4 className="font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
                    {action.name}
                  </h4>
                  <p className="text-sm text-gray-600 mt-1">
                    {action.description}
                  </p>
                </Link>
              ))}
            </div>
          </div>

          {/* Recent Activity */}
          <RecentActivity 
            activities={recentActivities}
            loading={loading}
          />

          {/* Tips & Getting Started */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200 p-6">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <BoltIcon className="w-5 h-5 text-white" />
                </div>
              </div>
              <div className="ml-4 flex-1">
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Pro Tip: Maximize Your SEO Impact
                </h3>
                <p className="text-gray-600 mb-4">
                  Start by analyzing your top competitors to understand what's working in your niche. 
                  Then use our AI to generate content that outperforms them with better structure, keywords, and user intent targeting.
                </p>
                <div className="flex flex-wrap gap-3">
                  <Link
                    href="/research/competitors"
                    className="btn btn-primary btn-sm"
                  >
                    Analyze Competitors
                  </Link>
                  <Link
                    href="/help/getting-started"
                    className="btn btn-secondary btn-sm"
                  >
                    View Tutorial
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </AuthenticatedLayout>
    </ProtectedRoute>
  );
}